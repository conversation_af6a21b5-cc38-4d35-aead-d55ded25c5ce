const express = require('express');
const cors = require('cors');
const https = require('https');

const app = express();
const PORT = 7008;

app.use(cors());
app.use(express.json());

const callYourAPI = async (profileUrl, prompt) => {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify({
            url: profileUrl,
            prompt: prompt
        });

        const options = {
            hostname: 'localhost',
            port: 7007,
            path: '/api/linkedin/messages',
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'accept': '*/*',
                'Content-Length': Buffer.byteLength(postData)
            },
            rejectUnauthorized: false
        };

        const req = https.request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                try {
                    console.log('Raw API response:', data);
                    const response = JSON.parse(data);
                    console.log('Parsed API response:', response);
                    resolve(response);
                } catch (error) {
                    console.error('Failed to parse API response:', data);
                    reject(new Error('Invalid JSON response from API'));
                }
            });
        });

        req.on('error', (error) => {
            reject(error);
        });

        req.write(postData);
        req.end();
    });
};

app.post('/api/linkedin/messages', async (req, res) => {
    try {
        const { url, prompt } = req.body;

        if (!url) {
            return res.status(400).json({
                error: 'Profile URL is required',
                message: 'Please provide a valid LinkedIn profile URL'
            });
        }

        console.log('Proxy received request:', { url, prompt });
        const apiResponse = await callYourAPI(url, prompt);

        res.json(apiResponse);

    } catch (error) {
        res.status(500).json({ error: error.message });
    }
});

app.listen(PORT, () => {
    console.log(`🚀 Proxy server running on http://localhost:${PORT}`);
});
